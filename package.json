{"name": "streamit-pro", "version": "2.0.0", "description": "Professional multi-participant video conferencing platform with WebRTC, noise cancellation, and real-time communication", "main": "server.js", "scripts": {"start": "node server.js", "build": "echo 'No build step needed - static HTML app'", "dev": "node server.js", "heroku-postbuild": "echo 'Static app ready for deployment'"}, "keywords": ["video-conferencing", "webrtc", "react", "recording", "chat", "screen-sharing", "background-effects", "professional", "meeting", "multi-participant", "noise-cancellation"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/joelgriiyo/streamit2.git"}, "homepage": "https://github.com/joelgriiyo/streamit2", "engines": {"node": "20.x", "npm": "10.x"}, "dependencies": {"express": "^4.18.2", "lucide-react": "^0.511.0", "socket.io": "^4.8.1"}}