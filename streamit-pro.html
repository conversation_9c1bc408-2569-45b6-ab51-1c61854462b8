<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StreamIt Pro - Professional Video Conferencing</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="/socket.io/socket.io.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated gradient background */
        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* Floating particles background */
        .animated-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }

        .floating-particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .floating-particle:nth-child(1) {
            width: 80px;
            height: 80px;
            left: 10%;
            animation-delay: 0s;
            animation-duration: 6s;
        }

        .floating-particle:nth-child(2) {
            width: 60px;
            height: 60px;
            left: 20%;
            animation-delay: 1s;
            animation-duration: 8s;
        }

        .floating-particle:nth-child(3) {
            width: 100px;
            height: 100px;
            left: 35%;
            animation-delay: 2s;
            animation-duration: 7s;
        }

        .floating-particle:nth-child(4) {
            width: 40px;
            height: 40px;
            left: 50%;
            animation-delay: 3s;
            animation-duration: 9s;
        }

        .floating-particle:nth-child(5) {
            width: 70px;
            height: 70px;
            left: 65%;
            animation-delay: 4s;
            animation-duration: 6s;
        }

        .floating-particle:nth-child(6) {
            width: 90px;
            height: 90px;
            left: 80%;
            animation-delay: 5s;
            animation-duration: 8s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Enhanced glass effect with subtle pulse animation */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2); 
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .gradient-text { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            -webkit-background-clip: text; 
            -webkit-text-fill-color: transparent; 
            background-clip: text;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: 200% 200%;
            color: white;
            padding: 14px 28px;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            font-size: 16px;
            position: relative;
            overflow: hidden;
            animation: buttonGlow 3s ease-in-out infinite alternate;
        }

        .btn-primary:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            background-position: 100% 0;
        }

        .btn-primary:active {
            transform: translateY(0) scale(0.98);
        }

        @keyframes buttonGlow {
            0% {
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }
            100% {
                box-shadow: 0 4px 20px rgba(102, 126, 234, 0.6);
            }
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 14px 28px;
            border-radius: 12px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }
        
        .video-tile {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            border-radius: 16px;
            position: relative;
            aspect-ratio: 16/9;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            animation: videoTileFloat 6s ease-in-out infinite;
        }

        .video-tile:hover {
            transform: translateY(-5px) scale(1.02);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        @keyframes videoTileFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-3px);
            }
        }
        
        .control-button { 
            padding: 16px; 
            border-radius: 50%; 
            border: none; 
            cursor: pointer; 
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .control-button:hover { 
            transform: scale(1.1); 
        }
        
        .control-active { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.5); 
        }
        
        .control-inactive { 
            background: rgba(255, 255, 255, 0.1); 
            color: white; 
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .control-danger { 
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%); 
            color: white; 
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }
        
        .volume-meter { 
            width: 100%; 
            height: 6px; 
            background: rgba(255, 255, 255, 0.2); 
            border-radius: 3px; 
            overflow: hidden; 
        }
        
        .volume-level { 
            height: 100%; 
            background: linear-gradient(90deg, #4ade80 0%, #fbbf24 50%, #ef4444 100%); 
            transition: width 0.1s ease; 
        }
        
        .recording-indicator { 
            animation: pulse 2s infinite; 
        }
        
        @keyframes pulse { 
            0%, 100% { opacity: 1; } 
            50% { opacity: 0.5; } 
        }
        
        .connection-quality-excellent { color: #4ade80; }
        .connection-quality-good { color: #fbbf24; }
        .connection-quality-poor { color: #ef4444; }
        
        .participant-speaking { 
            border: 3px solid #4ade80; 
            box-shadow: 0 0 30px rgba(74, 222, 128, 0.4); 
        }
        
        .blur-background { 
            backdrop-filter: blur(15px); 
        }
        
        .virtual-background { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
        }
        
        .animate-fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        .animate-slide-up {
            animation: slideUp 0.6s ease-out;
        }

        .animate-bounce-in {
            animation: bounceIn 0.8s ease-out;
        }

        .animate-scale-in {
            animation: scaleIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { transform: translateY(40px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3) translateY(30px);
            }
            50% {
                opacity: 1;
                transform: scale(1.05) translateY(-10px);
            }
            70% {
                transform: scale(0.95) translateY(5px);
            }
            100% {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8) rotate(-5deg);
            }
            to {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }
        
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .meeting-link-container {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            margin: 20px 0;
        }
        
        .copy-button {
            background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .copy-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(74, 222, 128, 0.4);
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            animation: slideInRight 0.3s ease-out;
        }
        
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .input-field {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 14px 18px;
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            font-size: 16px;
            width: 100%;
        }
        
        .input-field:focus {
            outline: none;
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .input-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .chat-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .chat-message {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 12px 16px;
            margin-bottom: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .settings-modal {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .device-selector {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px 14px;
            color: white;
            backdrop-filter: blur(10px);
            width: 100%;
        }
        
        .device-selector:focus {
            outline: none;
            border-color: rgba(102, 126, 234, 0.5);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }
    </style>
</head>
<body>
    <!-- Animated Background with Enhanced Floating Particles -->
    <div class="animated-background">
        <div class="floating-particle"></div>
        <div class="floating-particle"></div>
        <div class="floating-particle"></div>
        <div class="floating-particle"></div>
        <div class="floating-particle"></div>
        <div class="floating-particle"></div>
    </div>

    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef, useCallback, createContext, useContext } = React;

        // Notification Context
        const NotificationContext = createContext();

        const NotificationProvider = ({ children }) => {
            const [notifications, setNotifications] = useState([]);

            const addNotification = (message, type = 'info', duration = 3000) => {
                const id = Date.now();
                const notification = { id, message, type, duration };
                setNotifications(prev => [...prev, notification]);

                setTimeout(() => {
                    setNotifications(prev => prev.filter(n => n.id !== id));
                }, duration);
            };

            return (
                <NotificationContext.Provider value={{ addNotification }}>
                    {children}
                    <div className="fixed top-4 right-4 z-50 space-y-2">
                        {notifications.map(notification => (
                            <div key={notification.id} className="notification">
                                <div className="flex items-center space-x-2">
                                    {notification.type === 'success' && (
                                        <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    )}
                                    {notification.type === 'error' && (
                                        <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    )}
                                    {notification.type === 'info' && (
                                        <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    )}
                                    <span className="text-gray-800 font-medium">{notification.message}</span>
                                </div>
                            </div>
                        ))}
                    </div>
                </NotificationContext.Provider>
            );
        };

        // Advanced Media Manager Class
        class AdvancedMediaManager {
            constructor() {
                this.localStream = null;
                this.mediaRecorder = null;
                this.recordedChunks = [];
                this.audioContext = null;
                this.analyser = null;
                this.devices = { cameras: [], microphones: [], speakers: [] };
                this.currentDevices = { camera: null, microphone: null, speaker: null };
                this.backgroundMode = 'none';
                this.isRecording = false;
                this.participants = new Map();
                this.currentUser = null;

                // WebRTC properties
                this.socket = null;
                this.meetingId = null;
                this.peerConnections = new Map();
                this.remoteStreams = new Map();
                this.activeSpeaker = null;
                this.onParticipantUpdate = null;
                this.onChatMessage = null;

                // WebRTC configuration with STUN servers
                this.rtcConfig = {
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' },
                        { urls: 'stun:stun1.l.google.com:19302' },
                        { urls: 'stun:stun2.l.google.com:19302' }
                    ]
                };
            }

            async initialize() {
                try {
                    await this.getDevices();
                    await this.requestPermissions();
                    return true;
                } catch (error) {
                    console.error('Failed to initialize media manager:', error);
                    return false;
                }
            }

            async requestPermissions() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ 
                        video: true, 
                        audio: true 
                    });
                    stream.getTracks().forEach(track => track.stop());
                    return true;
                } catch (error) {
                    throw new Error('Camera and microphone permissions are required');
                }
            }

            async getDevices() {
                try {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    this.devices = {
                        cameras: devices.filter(device => device.kind === 'videoinput'),
                        microphones: devices.filter(device => device.kind === 'audioinput'),
                        speakers: devices.filter(device => device.kind === 'audiooutput')
                    };

                    // Set default devices
                    if (this.devices.cameras.length > 0) {
                        this.currentDevices.camera = this.devices.cameras[0];
                    }
                    if (this.devices.microphones.length > 0) {
                        this.currentDevices.microphone = this.devices.microphones[0];
                    }
                    if (this.devices.speakers.length > 0) {
                        this.currentDevices.speaker = this.devices.speakers[0];
                    }

                    return this.devices;
                } catch (error) {
                    console.error('Error getting devices:', error);
                    return this.devices;
                }
            }

            async startStream(constraints = { video: true, audio: true }) {
                try {
                    this.stopStream();

                    const streamConstraints = {
                        video: constraints.video ? {
                            deviceId: this.currentDevices.camera?.deviceId,
                            width: { ideal: 1920, max: 1920 },
                            height: { ideal: 1080, max: 1080 },
                            frameRate: { ideal: 30, max: 60 }
                        } : false,
                        audio: constraints.audio ? {
                            deviceId: this.currentDevices.microphone?.deviceId,
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true,
                            sampleRate: 48000,
                            channelCount: 1,
                            latency: 0.01,
                            volume: 1.0
                        } : false
                    };

                    this.localStream = await navigator.mediaDevices.getUserMedia(streamConstraints);

                    if (constraints.audio) {
                        this.setupAudioAnalysis();
                    }

                    return this.localStream;
                } catch (error) {
                    console.error('Error starting stream:', error);
                    throw error;
                }
            }

            setupAudioAnalysis() {
                try {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.analyser = this.audioContext.createAnalyser();
                    const microphone = this.audioContext.createMediaStreamSource(this.localStream);

                    this.analyser.fftSize = 256;
                    microphone.connect(this.analyser);
                } catch (error) {
                    console.error('Error setting up audio analysis:', error);
                }
            }

            getAudioLevel() {
                if (!this.analyser) return 0;

                const bufferLength = this.analyser.frequencyBinCount;
                const dataArray = new Uint8Array(bufferLength);
                this.analyser.getByteFrequencyData(dataArray);

                let sum = 0;
                for (let i = 0; i < bufferLength; i++) {
                    sum += dataArray[i];
                }
                return (sum / bufferLength) / 255;
            }

            async startRecording() {
                if (!this.localStream) {
                    throw new Error('No stream available for recording');
                }

                this.recordedChunks = [];

                const options = {
                    mimeType: 'video/webm;codecs=vp9,opus',
                    videoBitsPerSecond: 2500000,
                    audioBitsPerSecond: 128000
                };

                try {
                    this.mediaRecorder = new MediaRecorder(this.localStream, options);
                } catch (error) {
                    this.mediaRecorder = new MediaRecorder(this.localStream);
                }

                this.mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        this.recordedChunks.push(event.data);
                    }
                };

                this.mediaRecorder.onstop = () => {
                    this.saveRecording();
                };

                this.mediaRecorder.start(1000);
                this.isRecording = true;
                return true;
            }

            stopRecording() {
                if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
                    this.mediaRecorder.stop();
                    this.isRecording = false;
                    return true;
                }
                return false;
            }

            saveRecording() {
                if (this.recordedChunks.length === 0) return;

                const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = `StreamIt-Recording-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                URL.revokeObjectURL(url);
                this.recordedChunks = [];

                console.log('Recording saved to Downloads folder');
            }

            toggleMute() {
                if (this.localStream) {
                    const audioTrack = this.localStream.getAudioTracks()[0];
                    if (audioTrack) {
                        audioTrack.enabled = !audioTrack.enabled;
                        return !audioTrack.enabled;
                    }
                }
                return false;
            }

            toggleVideo() {
                if (this.localStream) {
                    const videoTrack = this.localStream.getVideoTracks()[0];
                    if (videoTrack) {
                        videoTrack.enabled = !videoTrack.enabled;
                        return !videoTrack.enabled;
                    }
                }
                return false;
            }

            async switchCamera(deviceId) {
                this.currentDevices.camera = this.devices.cameras.find(d => d.deviceId === deviceId);
                await this.startStream({ video: true, audio: true });
            }

            async switchMicrophone(deviceId) {
                this.currentDevices.microphone = this.devices.microphones.find(d => d.deviceId === deviceId);
                await this.startStream({ video: true, audio: true });
            }

            setBackgroundMode(mode) {
                this.backgroundMode = mode;
                console.log('Background mode set to:', mode);
            }

            stopStream() {
                if (this.localStream) {
                    this.localStream.getTracks().forEach(track => {
                        track.stop();
                        console.log(`Stopped ${track.kind} track`);
                    });
                    this.localStream = null;
                }

                if (this.audioContext) {
                    this.audioContext.close();
                    this.audioContext = null;
                    this.analyser = null;
                }

                console.log('All media streams stopped and cleaned up');
            }

            // Meeting management
            generateMeetingId() {
                return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
            }

            getMeetingLink(meetingId) {
                return `${window.location.origin}${window.location.pathname}#/meeting/${meetingId}`;
            }

            // Real WebRTC connection management
            async connectToMeeting(meetingId, userName) {
                try {
                    this.meetingId = meetingId;
                    this.currentUser = { name: userName, id: 'local' };

                    // Initialize Socket.IO connection
                    this.socket = io();
                    this.setupSocketListeners();

                    // Start local stream with noise cancellation
                    await this.startStream({
                        video: true,
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true,
                            sampleRate: 48000
                        }
                    });

                    // Join the meeting
                    this.socket.emit('join-meeting', { meetingId, userName });

                    console.log(`✅ Connected ${userName} to meeting ${meetingId}`);
                    return true;
                } catch (error) {
                    console.error('Failed to connect to meeting:', error);
                    throw error;
                }
            }

            setupSocketListeners() {
                // Handle existing participants
                this.socket.on('existing-participants', (participants) => {
                    console.log('📋 Existing participants:', participants);
                    participants.forEach(participant => {
                        this.addParticipant(participant);
                        this.createPeerConnection(participant.id);
                    });
                });

                // Handle new participant joining
                this.socket.on('participant-joined', (participant) => {
                    console.log('👤 New participant joined:', participant);
                    this.addParticipant(participant);
                    this.createPeerConnection(participant.id, true); // true = initiator
                });

                // Handle participant leaving
                this.socket.on('participant-left', (participantId) => {
                    console.log('👋 Participant left:', participantId);
                    this.removeParticipant(participantId);
                    this.closePeerConnection(participantId);
                });

                // WebRTC signaling
                this.socket.on('offer', async (data) => {
                    await this.handleOffer(data);
                });

                this.socket.on('answer', async (data) => {
                    await this.handleAnswer(data);
                });

                this.socket.on('ice-candidate', async (data) => {
                    await this.handleIceCandidate(data);
                });

                // Chat messages
                this.socket.on('chat-message', (message) => {
                    if (this.onChatMessage) {
                        this.onChatMessage(message);
                    }
                });

                // Participant state updates
                this.socket.on('participant-audio-toggle', (data) => {
                    const participant = this.participants.get(data.participantId);
                    if (participant) {
                        participant.isMuted = data.isMuted;
                        this.updateParticipant(participant);
                    }
                });

                this.socket.on('participant-video-toggle', (data) => {
                    const participant = this.participants.get(data.participantId);
                    if (participant) {
                        participant.isVideoOff = data.isVideoOff;
                        this.updateParticipant(participant);
                    }
                });
            }

            async createPeerConnection(participantId, isInitiator = false) {
                try {
                    const peerConnection = new RTCPeerConnection(this.rtcConfig);
                    this.peerConnections.set(participantId, peerConnection);

                    // Add local stream tracks
                    if (this.localStream) {
                        this.localStream.getTracks().forEach(track => {
                            peerConnection.addTrack(track, this.localStream);
                        });
                    }

                    // Handle remote stream
                    peerConnection.ontrack = (event) => {
                        console.log('📺 Received remote stream from:', participantId);
                        const [remoteStream] = event.streams;
                        this.remoteStreams.set(participantId, remoteStream);
                        this.updateParticipant({ id: participantId, stream: remoteStream });
                    };

                    // Handle ICE candidates
                    peerConnection.onicecandidate = (event) => {
                        if (event.candidate) {
                            this.socket.emit('ice-candidate', {
                                target: participantId,
                                candidate: event.candidate
                            });
                        }
                    };

                    // Handle connection state changes
                    peerConnection.onconnectionstatechange = () => {
                        console.log(`🔗 Connection state with ${participantId}:`, peerConnection.connectionState);
                        if (peerConnection.connectionState === 'disconnected' ||
                            peerConnection.connectionState === 'failed') {
                            this.closePeerConnection(participantId);
                        }
                    };

                    // If initiator, create and send offer
                    if (isInitiator) {
                        const offer = await peerConnection.createOffer();
                        await peerConnection.setLocalDescription(offer);

                        this.socket.emit('offer', {
                            target: participantId,
                            offer: offer
                        });
                    }

                    console.log(`🤝 Peer connection created for ${participantId}`);
                } catch (error) {
                    console.error('Error creating peer connection:', error);
                }
            }

            async handleOffer(data) {
                try {
                    const peerConnection = this.peerConnections.get(data.sender);
                    if (!peerConnection) {
                        await this.createPeerConnection(data.sender);
                        return this.handleOffer(data);
                    }

                    await peerConnection.setRemoteDescription(data.offer);
                    const answer = await peerConnection.createAnswer();
                    await peerConnection.setLocalDescription(answer);

                    this.socket.emit('answer', {
                        target: data.sender,
                        answer: answer
                    });

                    console.log('📞 Handled offer from:', data.sender);
                } catch (error) {
                    console.error('Error handling offer:', error);
                }
            }

            async handleAnswer(data) {
                try {
                    const peerConnection = this.peerConnections.get(data.sender);
                    if (peerConnection) {
                        await peerConnection.setRemoteDescription(data.answer);
                        console.log('✅ Handled answer from:', data.sender);
                    }
                } catch (error) {
                    console.error('Error handling answer:', error);
                }
            }

            async handleIceCandidate(data) {
                try {
                    const peerConnection = this.peerConnections.get(data.sender);
                    if (peerConnection) {
                        await peerConnection.addIceCandidate(data.candidate);
                        console.log('🧊 Added ICE candidate from:', data.sender);
                    }
                } catch (error) {
                    console.error('Error handling ICE candidate:', error);
                }
            }

            closePeerConnection(participantId) {
                const peerConnection = this.peerConnections.get(participantId);
                if (peerConnection) {
                    peerConnection.close();
                    this.peerConnections.delete(participantId);
                }
                this.remoteStreams.delete(participantId);
                console.log(`🔌 Closed peer connection for ${participantId}`);
            }

            addParticipant(participant) {
                this.participants.set(participant.id, participant);
                this.updateParticipant(participant);
            }

            removeParticipant(participantId) {
                this.participants.delete(participantId);
                this.updateParticipant({ id: participantId, removed: true });
            }

            updateParticipant(participant) {
                if (this.onParticipantUpdate) {
                    this.onParticipantUpdate(participant);
                }
            }

            getParticipants() {
                return Array.from(this.participants.values());
            }

            sendChatMessage(message) {
                if (this.socket) {
                    this.socket.emit('chat-message', { message });
                }
            }

            // Enhanced audio/video controls with WebRTC sync
            toggleMute() {
                if (this.localStream) {
                    const audioTrack = this.localStream.getAudioTracks()[0];
                    if (audioTrack) {
                        audioTrack.enabled = !audioTrack.enabled;
                        const isMuted = !audioTrack.enabled;
                        if (this.socket) {
                            this.socket.emit('toggle-audio', { isMuted });
                        }
                        return isMuted;
                    }
                }
                return false;
            }

            toggleVideo() {
                if (this.localStream) {
                    const videoTrack = this.localStream.getVideoTracks()[0];
                    if (videoTrack) {
                        videoTrack.enabled = !videoTrack.enabled;
                        const isVideoOff = !videoTrack.enabled;
                        if (this.socket) {
                            this.socket.emit('toggle-video', { isVideoOff });
                        }
                        return isVideoOff;
                    }
                }
                return false;
            }

            disconnect() {
                // Close all peer connections
                this.peerConnections.forEach((pc, id) => {
                    this.closePeerConnection(id);
                });

                // Close socket connection
                if (this.socket) {
                    this.socket.disconnect();
                    this.socket = null;
                }

                // Stop local stream
                this.stopStream();

                console.log('🔌 Disconnected from meeting');
            }
        }

        // Global media manager instance
        const mediaManager = new AdvancedMediaManager();

        // Router Hook
        const useRouter = () => {
            const [path, setPath] = useState(window.location.hash.slice(1) || '/');

            useEffect(() => {
                const handleHashChange = () => setPath(window.location.hash.slice(1) || '/');
                window.addEventListener('hashchange', handleHashChange);
                return () => window.removeEventListener('hashchange', handleHashChange);
            }, []);

            const navigate = (newPath) => {
                window.location.hash = newPath;
            };

            return { path, navigate };
        };

        // Name Input Modal Component
        const NameInputModal = ({ isOpen, onSubmit, onClose }) => {
            const [name, setName] = useState('');
            const [isLoading, setIsLoading] = useState(false);

            const handleSubmit = async (e) => {
                e.preventDefault();
                if (name.trim()) {
                    setIsLoading(true);
                    await onSubmit(name.trim());
                    setIsLoading(false);
                }
            };

            if (!isOpen) return null;

            return (
                <div className="modal-overlay">
                    <div className="modal-content animate-slide-up">
                        <div className="text-center mb-6">
                            <h2 className="text-3xl font-bold text-white mb-2">Join Meeting</h2>
                            <p className="text-white/80">Enter your name to start the meeting</p>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div>
                                <label className="block text-white font-medium mb-2">Your Name</label>
                                <input
                                    type="text"
                                    value={name}
                                    onChange={(e) => setName(e.target.value)}
                                    placeholder="Enter your full name"
                                    className="input-field"
                                    required
                                    autoFocus
                                    maxLength={50}
                                />
                            </div>

                            <div className="flex space-x-4">
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="btn-secondary flex-1"
                                    disabled={isLoading}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    className="btn-primary flex-1"
                                    disabled={!name.trim() || isLoading}
                                >
                                    {isLoading ? (
                                        <div className="flex items-center justify-center space-x-2">
                                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                            <span>Starting...</span>
                                        </div>
                                    ) : (
                                        'Start Meeting'
                                    )}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            );
        };

        // Volume Meter Component
        const VolumeMeter = ({ level = 0 }) => (
            <div className="volume-meter">
                <div
                    className="volume-level"
                    style={{ width: `${Math.min(level * 100, 100)}%` }}
                />
            </div>
        );

        // Advanced Video Tile Component
        const AdvancedVideoTile = ({ participant, isLocal = false, stream = null, isSpeaking = false, connectionQuality = 'excellent' }) => {
            const videoRef = useRef(null);
            const [audioLevel, setAudioLevel] = useState(0);

            useEffect(() => {
                if (videoRef.current && stream) {
                    videoRef.current.srcObject = stream;
                }
            }, [stream]);

            useEffect(() => {
                if (!isLocal && isSpeaking) {
                    const interval = setInterval(() => {
                        setAudioLevel(Math.random() * 0.8 + 0.2);
                    }, 100);
                    return () => clearInterval(interval);
                } else if (!isLocal) {
                    setAudioLevel(0);
                }
            }, [isLocal, isSpeaking]);

            useEffect(() => {
                if (isLocal) {
                    const interval = setInterval(() => {
                        const level = mediaManager.getAudioLevel();
                        setAudioLevel(level);
                    }, 100);
                    return () => clearInterval(interval);
                }
            }, [isLocal]);

            const getConnectionIcon = () => {
                const icons = {
                    excellent: "M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z",
                    good: "M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7z",
                    poor: "M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5z"
                };

                return (
                    <svg className={`w-4 h-4 connection-quality-${connectionQuality}`} fill="currentColor" viewBox="0 0 20 20">
                        <path d={icons[connectionQuality]} />
                    </svg>
                );
            };

            return (
                <div className={`video-tile ${isSpeaking ? 'participant-speaking' : ''}`}>
                    {participant.isVideoOff ? (
                        <div className={`flex items-center justify-center h-full text-white ${
                            mediaManager.backgroundMode === 'blur' ? 'blur-background' :
                            mediaManager.backgroundMode === 'virtual' ? 'virtual-background' : ''
                        }`}>
                            <div className="text-center">
                                <div className="w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mb-3 mx-auto">
                                    <span className="text-2xl font-bold">{participant.name[0]}</span>
                                </div>
                                <p className="text-lg font-medium">{participant.name}</p>
                                {isLocal && <p className="text-sm opacity-75">(You)</p>}
                            </div>
                        </div>
                    ) : (
                        <div className="relative h-full">
                            <video
                                ref={videoRef}
                                autoPlay
                                muted={isLocal}
                                playsInline
                                className={`w-full h-full object-cover ${isLocal ? 'scale-x-[-1]' : ''}`}
                            />

                            {!participant.isMuted && audioLevel > 0.1 && (
                                <div className="absolute top-3 left-3">
                                    <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs flex items-center space-x-1">
                                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                        <span>Speaking</span>
                                    </div>
                                </div>
                            )}

                            <div className="absolute top-3 right-3">
                                {getConnectionIcon()}
                            </div>

                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <span className="text-white font-medium">
                                            {participant.name} {isLocal && '(You)'}
                                        </span>
                                        {!participant.isMuted && audioLevel > 0 && (
                                            <div className="w-16 h-1 bg-gray-600 rounded-full overflow-hidden">
                                                <div
                                                    className="h-full bg-green-400 transition-all duration-100"
                                                    style={{ width: `${Math.min(audioLevel * 100, 100)}%` }}
                                                />
                                            </div>
                                        )}
                                    </div>

                                    <div className="flex items-center space-x-1">
                                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                                            participant.isMuted ? 'bg-red-500' : 'bg-green-500'
                                        }`}>
                                            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={
                                                    participant.isMuted
                                                        ? "M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2"
                                                        : "M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                                                } />
                                            </svg>
                                        </div>

                                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                                            participant.isVideoOff ? 'bg-red-500' : 'bg-green-500'
                                        }`}>
                                            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={
                                                    participant.isVideoOff
                                                        ? "M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"
                                                        : "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                                                } />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
        };

        // Enhanced Chat Component
        const EnhancedChat = ({ isOpen, onClose, messages, onSendMessage, participantCount }) => {
            const [message, setMessage] = useState('');
            const messagesEndRef = useRef(null);

            useEffect(() => {
                messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
            }, [messages]);

            const handleSendMessage = (e) => {
                e.preventDefault();
                if (message.trim()) {
                    onSendMessage(message.trim());
                    setMessage('');
                }
            };

            if (!isOpen) return null;

            return (
                <div className="w-80 h-full chat-container">
                    <div className="p-4 border-b border-white/10 flex justify-between items-center">
                        <div>
                            <h3 className="font-semibold text-white text-lg">Chat</h3>
                            <p className="text-xs text-white/60">{participantCount} participants</p>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 rounded-lg hover:bg-white/10 text-white/70 hover:text-white transition-colors"
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div className="flex-1 overflow-y-auto p-4 space-y-3">
                        {messages.map(msg => (
                            <div key={msg.id} className={msg.isSystem ? 'text-center' : ''}>
                                {msg.isSystem ? (
                                    <div className="text-xs text-white/60 bg-white/10 rounded-full px-3 py-1 inline-block">
                                        <svg className="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                        </svg>
                                        {msg.content}
                                    </div>
                                ) : (
                                    <div className="chat-message">
                                        <div className="flex items-center space-x-2 mb-2">
                                            <div className="w-6 h-6 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                                                <span className="text-xs font-bold text-white">{msg.sender[0]}</span>
                                            </div>
                                            <span className="text-sm font-medium text-white">{msg.sender}</span>
                                            <span className="text-xs text-white/50">
                                                {msg.timestamp?.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                            </span>
                                        </div>
                                        <div className="text-sm text-white/90 ml-8">
                                            {msg.content}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ))}
                        <div ref={messagesEndRef} />
                    </div>

                    <div className="p-4 border-t border-white/10">
                        <form onSubmit={handleSendMessage}>
                            <div className="flex space-x-2">
                                <input
                                    type="text"
                                    value={message}
                                    onChange={(e) => setMessage(e.target.value)}
                                    placeholder="Type a message..."
                                    className="input-field flex-1 text-sm"
                                    maxLength={500}
                                />
                                <button
                                    type="submit"
                                    disabled={!message.trim()}
                                    className="btn-primary px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                </button>
                            </div>
                        </form>

                        <div className="flex items-center justify-between mt-2 text-xs text-white/50">
                            <span>Press Enter to send</span>
                            <span>{message.length}/500</span>
                        </div>
                    </div>
                </div>
            );
        };

        // Settings Modal Component
        const SettingsModal = ({ isOpen, onClose, devices, currentDevices, onDeviceChange, backgroundMode, onBackgroundChange }) => {
            const [micLevel, setMicLevel] = useState(0);
            const [isTesting, setIsTesting] = useState(false);

            const testMicrophone = async () => {
                setIsTesting(true);
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const analyser = audioContext.createAnalyser();
                    const microphone = audioContext.createMediaStreamSource(stream);

                    analyser.fftSize = 256;
                    microphone.connect(analyser);

                    const checkLevel = () => {
                        const bufferLength = analyser.frequencyBinCount;
                        const dataArray = new Uint8Array(bufferLength);
                        analyser.getByteFrequencyData(dataArray);

                        let sum = 0;
                        for (let i = 0; i < bufferLength; i++) {
                            sum += dataArray[i];
                        }
                        const level = (sum / bufferLength) / 255;
                        setMicLevel(level);

                        if (isTesting) {
                            requestAnimationFrame(checkLevel);
                        }
                    };

                    checkLevel();

                    setTimeout(() => {
                        stream.getTracks().forEach(track => track.stop());
                        audioContext.close();
                        setIsTesting(false);
                        setMicLevel(0);
                    }, 3000);
                } catch (error) {
                    console.error('Mic test failed:', error);
                    setIsTesting(false);
                }
            };

            if (!isOpen) return null;

            return (
                <div className="modal-overlay">
                    <div className="settings-modal">
                        <div className="flex items-center justify-between p-6 border-b border-gray-200">
                            <h2 className="text-2xl font-semibold text-gray-800">Settings</h2>
                            <button
                                onClick={onClose}
                                className="p-2 rounded-lg hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
                            >
                                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>

                        <div className="p-6 overflow-y-auto max-h-[60vh] space-y-6">
                            {/* Camera Settings */}
                            <div>
                                <h3 className="text-lg font-semibold mb-4 text-gray-800">Camera</h3>
                                <select
                                    value={currentDevices.camera?.deviceId || ''}
                                    onChange={(e) => onDeviceChange('camera', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-purple-500 bg-white text-gray-800"
                                >
                                    <option value="">Select Camera</option>
                                    {devices.cameras.map(device => (
                                        <option key={device.deviceId} value={device.deviceId}>
                                            {device.label || `Camera ${device.deviceId.slice(0, 8)}`}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            {/* Microphone Settings */}
                            <div>
                                <h3 className="text-lg font-semibold mb-4 text-gray-800">Microphone</h3>
                                <select
                                    value={currentDevices.microphone?.deviceId || ''}
                                    onChange={(e) => onDeviceChange('microphone', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-purple-500 mb-4 bg-white text-gray-800"
                                >
                                    <option value="">Select Microphone</option>
                                    {devices.microphones.map(device => (
                                        <option key={device.deviceId} value={device.deviceId}>
                                            {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                                        </option>
                                    ))}
                                </select>

                                <div className="mt-4">
                                    <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-gray-700">Microphone Test</span>
                                        <button
                                            onClick={testMicrophone}
                                            disabled={isTesting}
                                            className="btn-primary text-sm px-4 py-2"
                                        >
                                            {isTesting ? 'Testing...' : 'Test Mic'}
                                        </button>
                                    </div>
                                    <VolumeMeter level={micLevel} />
                                    <p className="text-xs text-gray-500 mt-1">
                                        {isTesting ? 'Speak into your microphone...' : 'Click "Test Mic" to check your microphone level'}
                                    </p>
                                </div>
                            </div>

                            {/* Background Effects */}
                            <div>
                                <h3 className="text-lg font-semibold mb-4 text-gray-800">Background Effects</h3>
                                <div className="grid grid-cols-3 gap-3">
                                    <button
                                        onClick={() => onBackgroundChange('none')}
                                        className={`p-4 rounded-lg border-2 transition-all ${
                                            backgroundMode === 'none'
                                                ? 'border-purple-500 bg-purple-50'
                                                : 'border-gray-200 hover:border-gray-300'
                                        }`}
                                    >
                                        <div className="w-full h-16 bg-gray-200 rounded mb-2"></div>
                                        <span className="text-sm font-medium text-gray-800">None</span>
                                    </button>
                                    <button
                                        onClick={() => onBackgroundChange('blur')}
                                        className={`p-4 rounded-lg border-2 transition-all ${
                                            backgroundMode === 'blur'
                                                ? 'border-purple-500 bg-purple-50'
                                                : 'border-gray-200 hover:border-gray-300'
                                        }`}
                                    >
                                        <div className="w-full h-16 bg-gray-200 rounded mb-2 blur-sm"></div>
                                        <span className="text-sm font-medium text-gray-800">Blur</span>
                                    </button>
                                    <button
                                        onClick={() => onBackgroundChange('virtual')}
                                        className={`p-4 rounded-lg border-2 transition-all ${
                                            backgroundMode === 'virtual'
                                                ? 'border-purple-500 bg-purple-50'
                                                : 'border-gray-200 hover:border-gray-300'
                                        }`}
                                    >
                                        <div className="w-full h-16 virtual-background rounded mb-2"></div>
                                        <span className="text-sm font-medium text-gray-800">Virtual</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
                            <button
                                onClick={onClose}
                                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={onClose}
                                className="btn-primary"
                            >
                                Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            );
        };

        // Include external components
        const includeScript = (src) => {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        };

        // Home Page Component
        const HomePage = ({ navigate }) => {
            const [showNameModal, setShowNameModal] = useState(false);
            const { addNotification } = useContext(NotificationContext);

            const handleStartMeeting = async (userName) => {
                try {
                    const meetingId = mediaManager.generateMeetingId();
                    mediaManager.connectToMeeting(meetingId, userName);

                    addNotification(`Welcome ${userName}! Starting your meeting...`, 'success');
                    navigate(`/meeting/${meetingId}`);
                } catch (error) {
                    addNotification('Failed to start meeting: ' + error.message, 'error');
                }
            };

            return (
                <div className="min-h-screen flex items-center justify-center p-6">
                    <div className="max-w-6xl w-full">
                        <div className="text-center mb-16 animate-fade-in">
                            <h1 className="text-7xl font-bold text-white mb-6">
                                Stream<span className="gradient-text">It</span>
                            </h1>
                            <p className="text-2xl text-white/80 mb-4">Professional Video Conferencing</p>
                            <p className="text-lg text-white/60 max-w-2xl mx-auto">
                                Connect with anyone, anywhere. High-quality video calls with advanced features
                                like recording, background effects, and real-time collaboration.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8 mb-12">
                            <div className="glass-effect rounded-3xl p-8 text-center animate-slide-up" style={{animationDelay: '0.1s'}}>
                                <div className="w-20 h-20 bg-gradient-to-br from-blue-400 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-white mb-4">Start Meeting</h3>
                                <p className="text-white/70 mb-8 leading-relaxed">
                                    Begin an instant meeting with HD video, crystal clear audio, and professional features.
                                </p>
                                <button
                                    onClick={() => setShowNameModal(true)}
                                    className="btn-primary w-full text-lg py-4"
                                >
                                    Start Now
                                </button>
                            </div>

                            <div className="glass-effect rounded-3xl p-8 text-center animate-slide-up" style={{animationDelay: '0.2s'}}>
                                <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-white mb-4">Join Meeting</h3>
                                <p className="text-white/70 mb-8 leading-relaxed">
                                    Enter a meeting ID or click a shared link to join an existing meeting.
                                </p>
                                <button
                                    onClick={() => navigate('/join')}
                                    className="btn-secondary w-full text-lg py-4"
                                >
                                    Join Meeting
                                </button>
                            </div>

                            <div className="glass-effect rounded-3xl p-8 text-center animate-slide-up" style={{animationDelay: '0.3s'}}>
                                <div className="w-20 h-20 bg-gradient-to-br from-purple-400 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold text-white mb-4">Schedule</h3>
                                <p className="text-white/70 mb-8 leading-relaxed">
                                    Plan and schedule professional meetings and webinars for your team.
                                </p>
                                <button
                                    onClick={() => addNotification('Scheduling feature coming soon!', 'info')}
                                    className="btn-secondary w-full text-lg py-4"
                                >
                                    Coming Soon
                                </button>
                            </div>
                        </div>

                        <div className="text-center">
                            <div className="glass-effect rounded-2xl p-6 inline-block">
                                <div className="flex items-center space-x-6 text-white/80">
                                    <div className="flex items-center space-x-2">
                                        <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        <span>HD Video Quality</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        <span>Local Recording</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        <span>Background Effects</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        <span>Real-time Chat</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <NameInputModal
                        isOpen={showNameModal}
                        onSubmit={handleStartMeeting}
                        onClose={() => setShowNameModal(false)}
                    />
                </div>
            );
        };

        // Join Meeting Page Component
        const JoinMeetingPage = ({ navigate }) => {
            const [meetingId, setMeetingId] = useState('');
            const [userName, setUserName] = useState('');
            const [isJoining, setIsJoining] = useState(false);
            const { addNotification } = useContext(NotificationContext);

            const handleJoinMeeting = async (e) => {
                e.preventDefault();
                if (!meetingId.trim() || !userName.trim()) return;

                setIsJoining(true);
                try {
                    mediaManager.connectToMeeting(meetingId.trim(), userName.trim());
                    addNotification(`Joining meeting as ${userName}...`, 'success');
                    navigate(`/meeting/${meetingId.trim()}`);
                } catch (error) {
                    addNotification('Failed to join meeting: ' + error.message, 'error');
                } finally {
                    setIsJoining(false);
                }
            };

            return (
                <div className="min-h-screen flex items-center justify-center p-6">
                    <div className="max-w-md w-full">
                        <div className="glass-effect rounded-3xl p-8 animate-slide-up">
                            <div className="text-center mb-8">
                                <h2 className="text-4xl font-bold text-white mb-3">Join Meeting</h2>
                                <p className="text-white/70">Enter your details to join an existing meeting</p>
                            </div>

                            <form onSubmit={handleJoinMeeting} className="space-y-6">
                                <div>
                                    <label className="block text-white font-medium mb-2">Your Name</label>
                                    <input
                                        type="text"
                                        value={userName}
                                        onChange={(e) => setUserName(e.target.value)}
                                        placeholder="Enter your full name"
                                        className="input-field"
                                        required
                                        maxLength={50}
                                    />
                                </div>

                                <div>
                                    <label className="block text-white font-medium mb-2">Meeting ID</label>
                                    <input
                                        type="text"
                                        value={meetingId}
                                        onChange={(e) => setMeetingId(e.target.value)}
                                        placeholder="Enter meeting ID"
                                        className="input-field"
                                        required
                                    />
                                </div>

                                <button
                                    type="submit"
                                    className="btn-primary w-full text-lg py-4"
                                    disabled={!meetingId.trim() || !userName.trim() || isJoining}
                                >
                                    {isJoining ? (
                                        <div className="flex items-center justify-center space-x-2">
                                            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                            <span>Joining...</span>
                                        </div>
                                    ) : (
                                        'Join Meeting'
                                    )}
                                </button>
                            </form>

                            <div className="text-center mt-6">
                                <button
                                    onClick={() => navigate('/')}
                                    className="text-white/70 hover:text-white transition-colors"
                                >
                                    ← Back to Home
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // Meeting Link Share Component
        const MeetingLinkShare = ({ meetingId, isVisible, onClose }) => {
            const [copied, setCopied] = useState(false);
            const { addNotification } = useContext(NotificationContext);

            const meetingLink = mediaManager.getMeetingLink(meetingId);

            const copyToClipboard = async () => {
                try {
                    await navigator.clipboard.writeText(meetingLink);
                    setCopied(true);
                    addNotification('Meeting link copied to clipboard!', 'success');
                    setTimeout(() => setCopied(false), 2000);
                } catch (error) {
                    addNotification('Failed to copy link', 'error');
                }
            };

            if (!isVisible) return null;

            return (
                <div className="meeting-link-container">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-white font-semibold text-lg">Share Meeting Link</h3>
                        <button
                            onClick={onClose}
                            className="text-white/70 hover:text-white transition-colors"
                        >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <div className="flex items-center space-x-3 mb-4">
                        <input
                            type="text"
                            value={meetingLink}
                            readOnly
                            className="input-field flex-1 text-sm"
                        />
                        <button
                            onClick={copyToClipboard}
                            className={`copy-button ${copied ? 'bg-green-500' : ''}`}
                        >
                            {copied ? (
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            ) : (
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            )}
                        </button>
                    </div>

                    <p className="text-white/60 text-sm">
                        Share this link with others to invite them to your meeting. Anyone with this link can join.
                    </p>
                </div>
            );
        };

        // Complete Video Call Page Component (Simplified for file size)
        const CompleteVideoCallPage = ({ navigate, meetingId }) => {
            const [isConnected, setIsConnected] = useState(false);
            const [isMuted, setIsMuted] = useState(false);
            const [isVideoOff, setIsVideoOff] = useState(false);
            const [isScreenSharing, setIsScreenSharing] = useState(false);
            const [isRecording, setIsRecording] = useState(false);
            const [isChatOpen, setIsChatOpen] = useState(false);
            const [isSettingsOpen, setIsSettingsOpen] = useState(false);
            const [showLinkShare, setShowLinkShare] = useState(false);
            const [duration, setDuration] = useState(0);
            const [localStream, setLocalStream] = useState(null);
            const [devices, setDevices] = useState({ cameras: [], microphones: [], speakers: [] });
            const [currentDevices, setCurrentDevices] = useState({ camera: null, microphone: null, speaker: null });
            const [backgroundMode, setBackgroundMode] = useState('none');
            const { addNotification } = useContext(NotificationContext);

            const [participants, setParticipants] = useState([]);
            const [messages, setMessages] = useState([
                { id: 1, sender: 'System', content: `Welcome to meeting ${meetingId}!`, timestamp: new Date(), isSystem: true }
            ]);

            // Initialize WebRTC connection and media
            useEffect(() => {
                const initializeConnection = async () => {
                    try {
                        // Set up callbacks for participant updates
                        mediaManager.onParticipantUpdate = (participant) => {
                            if (participant.removed) {
                                setParticipants(prev => prev.filter(p => p.id !== participant.id));
                            } else {
                                setParticipants(prev => {
                                    const existing = prev.find(p => p.id === participant.id);
                                    if (existing) {
                                        return prev.map(p => p.id === participant.id ? { ...p, ...participant } : p);
                                    } else {
                                        return [...prev, { ...participant, isLocal: false }];
                                    }
                                });
                            }
                        };

                        // Set up chat message callback
                        mediaManager.onChatMessage = (message) => {
                            setMessages(prev => [...prev, message]);
                        };

                        // Get devices first
                        const deviceList = await mediaManager.getDevices();
                        setDevices(deviceList);
                        setCurrentDevices({
                            camera: mediaManager.currentDevices.camera,
                            microphone: mediaManager.currentDevices.microphone,
                            speaker: mediaManager.currentDevices.speaker
                        });

                        // Connect to meeting (this will start the stream and WebRTC)
                        await mediaManager.connectToMeeting(meetingId, mediaManager.currentUser?.name || 'Anonymous');

                        // Add local participant
                        setParticipants(prev => [...prev, {
                            id: 'local',
                            name: mediaManager.currentUser?.name || 'You',
                            isLocal: true,
                            isMuted: false,
                            isVideoOff: false,
                            stream: mediaManager.localStream
                        }]);

                        setLocalStream(mediaManager.localStream);
                        setIsConnected(true);

                        addNotification('Successfully connected to meeting!', 'success');
                    } catch (error) {
                        console.error('Failed to initialize connection:', error);
                        addNotification('Failed to connect to meeting: ' + error.message, 'error');
                        setIsConnected(true);
                    }
                };

                const timer = setTimeout(initializeConnection, 1000);
                return () => clearTimeout(timer);
            }, [meetingId]);

            // Duration timer
            useEffect(() => {
                if (!isConnected) return;
                const interval = setInterval(() => setDuration(d => d + 1), 1000);
                return () => clearInterval(interval);
            }, [isConnected]);

            // Cleanup on unmount
            useEffect(() => {
                return () => {
                    mediaManager.disconnect();
                };
            }, []);

            // Control handlers
            const handleToggleMute = () => {
                const muted = mediaManager.toggleMute();
                setIsMuted(muted);

                // Update local participant state
                setParticipants(prev => prev.map(p =>
                    p.isLocal ? { ...p, isMuted: muted } : p
                ));

                addNotification(muted ? 'Microphone muted' : 'Microphone unmuted', 'info');
            };

            const handleToggleVideo = () => {
                const videoOff = mediaManager.toggleVideo();
                setIsVideoOff(videoOff);

                // Update local participant state
                setParticipants(prev => prev.map(p =>
                    p.isLocal ? { ...p, isVideoOff: videoOff } : p
                ));

                addNotification(videoOff ? 'Camera turned off' : 'Camera turned on', 'info');
            };

            const handleToggleScreenShare = () => {
                setIsScreenSharing(!isScreenSharing);
                addNotification(isScreenSharing ? 'Screen sharing stopped' : 'Screen sharing started', 'info');
            };

            const handleToggleRecording = async () => {
                try {
                    if (isRecording) {
                        mediaManager.stopRecording();
                        setIsRecording(false);
                        addNotification('Recording saved to Downloads folder!', 'success');
                    } else {
                        await mediaManager.startRecording();
                        setIsRecording(true);
                        addNotification('Recording started', 'success');
                    }
                } catch (error) {
                    console.error('Recording error:', error);
                    addNotification('Recording failed: ' + error.message, 'error');
                }
            };

            const handleSendMessage = (content) => {
                // Send message through WebRTC
                mediaManager.sendChatMessage(content);
            };

            const handleDeviceChange = async (type, deviceId) => {
                try {
                    if (type === 'camera') {
                        await mediaManager.switchCamera(deviceId);
                        setCurrentDevices(prev => ({
                            ...prev,
                            camera: devices.cameras.find(d => d.deviceId === deviceId)
                        }));
                        const stream = mediaManager.localStream;
                        setLocalStream(stream);
                        addNotification('Camera switched successfully', 'success');
                    } else if (type === 'microphone') {
                        await mediaManager.switchMicrophone(deviceId);
                        setCurrentDevices(prev => ({
                            ...prev,
                            microphone: devices.microphones.find(d => d.deviceId === deviceId)
                        }));
                        const stream = mediaManager.localStream;
                        setLocalStream(stream);
                        addNotification('Microphone switched successfully', 'success');
                    }
                } catch (error) {
                    console.error('Device change error:', error);
                    addNotification('Failed to switch device: ' + error.message, 'error');
                }
            };

            const handleBackgroundChange = (mode) => {
                mediaManager.setBackgroundMode(mode);
                setBackgroundMode(mode);
                addNotification(`Background effect set to ${mode}`, 'info');
            };

            const handleEndCall = () => {
                if (confirm('Are you sure you want to end the call? All recordings will be stopped and media will be released.')) {
                    mediaManager.disconnect();
                    addNotification('Call ended. Disconnected from meeting.', 'info');
                    navigate('/');
                }
            };

            const formatDuration = (seconds) => {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;
                if (hours > 0) {
                    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                }
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            };

            if (!isConnected) {
                return (
                    <div className="h-screen bg-gradient-to-br from-gray-900 to-gray-800 flex items-center justify-center">
                        <div className="text-center text-white animate-fade-in">
                            <div className="w-24 h-24 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-8"></div>
                            <h2 className="text-4xl font-bold mb-4">Connecting to meeting...</h2>
                            <p className="text-gray-300 mb-2 text-xl">Meeting ID: {meetingId}</p>
                            <p className="text-gray-400">Initializing camera and microphone...</p>
                        </div>
                    </div>
                );
            }

            return (
                <div className="h-screen bg-gradient-to-br from-gray-900 to-gray-800 flex">
                    <div className={`flex-1 p-6 ${isChatOpen ? 'pr-0' : ''}`}>
                        {/* Enhanced Header */}
                        <div className="flex justify-between items-center mb-6 text-white">
                            <div>
                                <h1 className="text-3xl font-bold">Meeting: {meetingId}</h1>
                                <div className="flex items-center space-x-6 text-lg text-gray-300 mt-2">
                                    <span className="flex items-center space-x-2">
                                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                                        </svg>
                                        <span>{participants.length} participants</span>
                                    </span>
                                    <span>•</span>
                                    <span className="flex items-center space-x-2">
                                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                        </svg>
                                        <span>{formatDuration(duration)}</span>
                                    </span>
                                    <span>•</span>
                                    <span className="flex items-center space-x-2">
                                        <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                        <span>Connected</span>
                                    </span>
                                </div>
                            </div>
                            <div className="flex items-center space-x-4">
                                {isRecording && (
                                    <div className="flex items-center space-x-2 bg-red-500 text-white px-4 py-2 rounded-full recording-indicator">
                                        <div className="w-3 h-3 bg-white rounded-full"></div>
                                        <span className="font-medium">Recording</span>
                                    </div>
                                )}
                                <button
                                    onClick={() => setShowLinkShare(!showLinkShare)}
                                    className="btn-secondary px-4 py-2 text-sm"
                                >
                                    Share Link
                                </button>
                                <div className="text-sm text-gray-300 bg-black/30 px-3 py-2 rounded-full">
                                    {new Date().toLocaleTimeString()}
                                </div>
                            </div>
                        </div>

                        {/* Meeting Link Share */}
                        {showLinkShare && (
                            <MeetingLinkShare
                                meetingId={meetingId}
                                isVisible={showLinkShare}
                                onClose={() => setShowLinkShare(false)}
                            />
                        )}

                        {/* Video Grid */}
                        <div className={`grid gap-4 h-[calc(100vh-250px)] ${
                            participants.length === 1 ? 'grid-cols-1' :
                            participants.length === 2 ? 'grid-cols-1 lg:grid-cols-2' :
                            participants.length <= 4 ? 'grid-cols-2' :
                            'grid-cols-2 lg:grid-cols-3'
                        }`}>
                            {participants.map((participant, index) => {
                                const stream = participant.isLocal ? localStream :
                                             mediaManager.remoteStreams.get(participant.id);

                                return (
                                    <AdvancedVideoTile
                                        key={participant.id}
                                        participant={{
                                            ...participant,
                                            isMuted: participant.isLocal ? isMuted : participant.isMuted,
                                            isVideoOff: participant.isLocal ? isVideoOff : participant.isVideoOff
                                        }}
                                        isLocal={participant.isLocal}
                                        stream={stream}
                                        isSpeaking={false}
                                        connectionQuality="excellent"
                                    />
                                );
                            })}
                        </div>
                    </div>

                    {/* Enhanced Chat */}
                    <EnhancedChat
                        isOpen={isChatOpen}
                        onClose={() => setIsChatOpen(false)}
                        messages={messages}
                        onSendMessage={handleSendMessage}
                        participantCount={participants.length}
                    />

                    {/* Advanced Control Bar */}
                    <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50">
                        <div className="glass-effect rounded-3xl px-8 py-4 flex items-center space-x-4 shadow-2xl">
                            {/* Audio Controls */}
                            <button
                                onClick={handleToggleMute}
                                className={`control-button ${isMuted ? 'control-danger' : 'control-active'}`}
                                title={isMuted ? 'Unmute' : 'Mute'}
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={isMuted
                                        ? "M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2"
                                        : "M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                                    } />
                                </svg>
                            </button>

                            {/* Video Controls */}
                            <button
                                onClick={handleToggleVideo}
                                className={`control-button ${isVideoOff ? 'control-danger' : 'control-active'}`}
                                title={isVideoOff ? 'Turn on camera' : 'Turn off camera'}
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={isVideoOff
                                        ? "M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"
                                        : "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                                    } />
                                </svg>
                            </button>

                            {/* Screen Share */}
                            <button
                                onClick={handleToggleScreenShare}
                                className={`control-button ${isScreenSharing ? 'control-active' : 'control-inactive'}`}
                                title="Share screen"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </button>

                            <div className="w-px h-10 bg-white/20"></div>

                            {/* Chat */}
                            <button
                                onClick={() => setIsChatOpen(!isChatOpen)}
                                className={`control-button ${isChatOpen ? 'control-active' : 'control-inactive'}`}
                                title="Toggle chat"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                            </button>

                            {/* Record */}
                            <button
                                onClick={handleToggleRecording}
                                className={`control-button ${isRecording ? 'control-danger' : 'control-inactive'}`}
                                title={isRecording ? 'Stop recording' : 'Start recording'}
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                            </button>

                            {/* Settings */}
                            <button
                                onClick={() => setIsSettingsOpen(true)}
                                className="control-button control-inactive"
                                title="Settings"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </button>

                            <div className="w-px h-10 bg-white/20"></div>

                            {/* End Call */}
                            <button
                                onClick={handleEndCall}
                                className="control-button control-danger"
                                title="End call"
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 3l18 18" />
                                </svg>
                            </button>
                        </div>
                    </div>

                    {/* Settings Modal */}
                    <SettingsModal
                        isOpen={isSettingsOpen}
                        onClose={() => setIsSettingsOpen(false)}
                        devices={devices}
                        currentDevices={currentDevices}
                        onDeviceChange={handleDeviceChange}
                        backgroundMode={backgroundMode}
                        onBackgroundChange={handleBackgroundChange}
                    />
                </div>
            );
        };

        // Main App Component
        const App = () => {
            const { path, navigate } = useRouter();

            // Parse meeting ID from path
            const meetingMatch = path.match(/^\/meeting\/(.+)$/);
            const meetingId = meetingMatch ? meetingMatch[1].split('?')[0] : null;

            console.log('Current path:', path);
            console.log('Meeting ID:', meetingId);

            if (path === '/') {
                return <HomePage navigate={navigate} />;
            } else if (path === '/join') {
                return <JoinMeetingPage navigate={navigate} />;
            } else if (meetingId) {
                return <CompleteVideoCallPage navigate={navigate} meetingId={meetingId} />;
            } else {
                return <HomePage navigate={navigate} />;
            }
        };

        // Initialize and render the application
        const initializeApp = async () => {
            try {
                console.log('Initializing StreamIt Pro...');

                // Initialize media manager
                const initialized = await mediaManager.initialize();
                if (!initialized) {
                    console.warn('Media manager initialization failed, but continuing...');
                }

                console.log('Rendering React application...');

                // Render the app
                ReactDOM.render(
                    <NotificationProvider>
                        <App />
                    </NotificationProvider>,
                    document.getElementById('root')
                );

                console.log('StreamIt Pro initialized successfully!');
            } catch (error) {
                console.error('Failed to initialize application:', error);

                // Fallback error display
                document.getElementById('root').innerHTML = `
                    <div style="
                        min-height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        font-family: 'Inter', sans-serif;
                        text-align: center;
                        padding: 20px;
                    ">
                        <div>
                            <h1 style="font-size: 3rem; margin-bottom: 1rem;">StreamIt Pro</h1>
                            <p style="font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.8;">
                                Failed to initialize application
                            </p>
                            <p style="margin-bottom: 2rem; opacity: 0.7;">
                                ${error.message}
                            </p>
                            <button
                                onclick="window.location.reload()"
                                style="
                                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                    color: white;
                                    border: none;
                                    padding: 12px 24px;
                                    border-radius: 12px;
                                    font-size: 1rem;
                                    cursor: pointer;
                                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
                                "
                            >
                                Reload Application
                            </button>
                        </div>
                    </div>
                `;
            }
        };

        // Start the application when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }
    </script>
</body>
</html>
